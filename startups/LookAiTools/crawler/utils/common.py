"""
爬虫通用工具函数
"""
import os
import json
import time
from urllib.parse import urlparse, parse_qs, urlunparse, urljoin

def clean_text(text):
    """清理文本"""
    if not text:
        return ""
    # 清理多余的空白和换行
    return ' '.join(text.strip().split())

def clean_url(url):
    """清理URL，移除utm参数"""
    if not url:
        return ""
    
    try:
        parsed = urlparse(url)
        query_params = parse_qs(parsed.query)
        
        # 移除所有utm参数
        cleaned_params = {k: v for k, v in query_params.items() 
                        if not k.startswith('utm_')}
        
        if cleaned_params:
            query_string = '&'.join([f"{k}={'&'.join(v)}" for k, v in cleaned_params.items()])
            cleaned_parsed = parsed._replace(query=query_string)
        else:
            cleaned_parsed = parsed._replace(query='')
        
        return urlunparse(cleaned_parsed)
    except:
        return url

def resolve_url(url, base_url):
    """解析相对URL为绝对URL"""
    try:
        return urljoin(base_url, url)
    except:
        return url

def is_valid_image_url(url):
    """检查是否是有效的图片URL"""
    if not url:
        return False
    
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    url_lower = url.lower()
    
    return any(ext in url_lower for ext in image_extensions)

def is_valid_external_url(url):
    """检查是否是有效的外部URL"""
    if not url or not url.startswith('http'):
        return False
    
    excluded_domains = [
        'toolify.ai', 'seo.ing', 'google.com', 'amazon.com'
    ]
    
    for domain in excluded_domains:
        if domain in url.lower():
            return False
    
    return True

def safe_filename(name):
    """生成安全的文件名"""
    safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    return safe_name.replace(' ', '_')

def save_json(data, filepath):
    """保存JSON数据"""
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"❌ 保存JSON失败: {e}")
        return False

def get_timestamp():
    """获取时间戳"""
    return {
        "timestamp": time.time(),
        "datetime": time.strftime("%Y-%m-%d %H:%M:%S")
    }

async def save_screenshot(page, tool_name, platform="toolify"):
    """保存页面截图"""
    try:
        # 创建截图目录
        screenshot_dir = f"screenshots/{platform}"
        os.makedirs(screenshot_dir, exist_ok=True)
        
        # 生成文件名
        filename = safe_filename(tool_name)
        screenshot_path = f"{screenshot_dir}/{filename}.jpg"
        
        # 确保页面滚动到顶部
        await page.evaluate("window.scrollTo(0, 0)")
        await page.wait_for_timeout(1000)
        
        # 获取当前视窗大小
        viewport = await page.evaluate("""() => {
            return {
                width: window.innerWidth,
                height: window.innerHeight
            }
        }""")
        
        # 截图 - 只截取首屏可视区域
        await page.screenshot(
            path=screenshot_path, 
            full_page=False,
            type='jpeg', 
            quality=80,
            clip={
                'x': 0,
                'y': 0,
                'width': viewport['width'],
                'height': viewport['height']
            }
        )
        
        return screenshot_path
        
    except Exception as e:
        print(f"     ❌ 截图保存失败: {e}")
        return ""

def extract_page_meta(soup):
    """提取页面meta数据"""
    try:
        meta_data = {}
        
        # 提取所有meta标签
        meta_elems = soup.select('meta')
        for meta in meta_elems:
            name = meta.get('name') or meta.get('property') or meta.get('http-equiv')
            content = meta.get('content')
            
            if name and content:
                meta_data[name] = content
        
        # 提取Open Graph数据
        og_data = {}
        for meta in meta_elems:
            property_name = meta.get('property', '')
            if property_name.startswith('og:'):
                content = meta.get('content', '')
                if content:
                    og_data[property_name] = content
        
        if og_data:
            meta_data['open_graph'] = og_data
        
        # 提取Twitter Card数据
        twitter_data = {}
        for meta in meta_elems:
            name = meta.get('name', '')
            if name.startswith('twitter:'):
                content = meta.get('content', '')
                if content:
                    twitter_data[name] = content
        
        if twitter_data:
            meta_data['twitter_card'] = twitter_data
        
        return meta_data
        
    except Exception as e:
        return {}

def extract_page_text(soup):
    """提取完整页面文本"""
    try:
        # 移除script和style标签
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 获取所有文本
        text = soup.get_text()
        
        # 清理文本
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text[:5000]  # 限制长度
        
    except Exception as e:
        return ""

def extract_headings(soup):
    """提取所有标题"""
    try:
        headings = []
        for level in range(1, 7):  # h1 到 h6
            heading_elems = soup.select(f'h{level}')
            for elem in heading_elems:
                text = elem.get_text().strip()
                if text:
                    headings.append({
                        "level": level,
                        "text": text
                    })
        
        return headings[:20]  # 限制数量
        
    except Exception as e:
        return []

def extract_links(soup, base_url):
    """提取所有链接"""
    try:
        links = []
        link_elems = soup.select('a[href]')
        
        for elem in link_elems[:50]:  # 限制数量
            href = elem.get('href', '')
            text = elem.get_text().strip()
            
            if href and text:
                full_url = resolve_url(href, base_url)
                links.append({
                    "url": full_url,
                    "text": text[:100]  # 限制文本长度
                })
        
        return links
        
    except Exception as e:
        return []
