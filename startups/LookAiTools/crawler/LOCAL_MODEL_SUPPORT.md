# 本地模型支持说明

## 概述

AI工具数据标准化脚本现已支持本地部署的AI模型，提供了更灵活的部署选择。您可以在AWS Bedrock和本地模型之间自由切换。

## 支持的模型类型

### AWS Bedrock (默认)
- **模型**: Claude 3.5 Sonnet
- **优势**: 稳定可靠，无需本地部署
- **适用场景**: 生产环境，大规模处理

### 本地模型
- **API格式**: OpenAI兼容接口
- **优势**: 数据隐私，成本控制，离线使用
- **适用场景**: 敏感数据处理，开发测试

## 配置方法

### 1. 环境变量配置

在 `.env` 文件中添加本地模型配置：

```env
# 本地模型配置
LOCAL_API_URL=http://localhost:8989/v1/chat/completions
LOCAL_API_KEY=ki2api-key-2024
LOCAL_MODEL_NAME=claude-sonnet-4-20250514
```

### 2. 本地模型服务

确保您的本地模型服务支持以下API格式：

```bash
curl -X POST http://localhost:8989/v1/chat/completions \
  -H "Authorization: Bearer ki2api-key-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-sonnet-4-20250514",
    "messages": [
      {"role": "system", "content": "系统提示词"},
      {"role": "user", "content": "用户提示词"}
    ],
    "max_tokens": 3000,
    "temperature": 0.1
  }'
```

## 使用方法

### 命令行参数

```bash
# 使用AWS Bedrock (默认)
python data_standardizer.py

# 使用本地模型
python data_standardizer.py --local

# 指定输入输出文件
python data_standardizer.py --local --input custom.json --output output_dir
```

### 测试验证

```bash
# 测试本地模型连接
python test_local_model.py

# 测试AWS Bedrock
python test_refactored.py

# 运行示例
python example_usage.py
```

## 代码结构

### 核心类: AIStandardizer

```python
# 初始化AWS Bedrock模式
standardizer = AIStandardizer(use_local_model=False)

# 初始化本地模型模式
standardizer = AIStandardizer(use_local_model=True)

# 调用AI模型 (自动选择)
response = standardizer.call_ai_model(system_prompt, user_prompt)
```

### 内部实现

- `call_ai_model()`: 统一的AI调用接口
- `_call_bedrock_claude()`: AWS Bedrock实现
- `_call_local_model()`: 本地模型实现

## 性能对比

| 特性 | AWS Bedrock | 本地模型 |
|------|-------------|----------|
| 部署复杂度 | 低 | 中等 |
| 数据隐私 | 云端处理 | 本地处理 |
| 成本 | 按使用付费 | 硬件成本 |
| 稳定性 | 高 | 取决于硬件 |
| 响应速度 | 网络延迟 | 本地处理 |
| 扩展性 | 自动扩展 | 手动扩展 |

## 故障排除

### 本地模型连接失败

1. **检查服务状态**
   ```bash
   curl http://localhost:8989/health
   ```

2. **检查配置**
   - 确认 `LOCAL_API_URL` 正确
   - 确认 `LOCAL_API_KEY` 匹配
   - 确认 `LOCAL_MODEL_NAME` 存在

3. **查看日志**
   ```bash
   python test_local_model.py
   ```

### AWS Bedrock连接失败

1. **检查AWS配置**
   - 确认 `AWS_ACCESS_KEY_ID` 正确
   - 确认 `AWS_SECRET_ACCESS_KEY` 正确
   - 确认 `AWS_REGION` 支持Bedrock

2. **检查权限**
   - 确认IAM用户有Bedrock访问权限
   - 确认模型已启用

## 最佳实践

### 开发环境
- 使用本地模型进行开发和测试
- 快速迭代，降低成本

### 生产环境
- 使用AWS Bedrock确保稳定性
- 利用云端的高可用性

### 混合使用
- 敏感数据使用本地模型
- 大规模处理使用AWS Bedrock

## 扩展支持

当前实现支持OpenAI兼容的API格式，可以轻松扩展支持：

- Ollama
- LocalAI
- vLLM
- Text Generation WebUI
- 其他OpenAI兼容服务

只需修改 `LOCAL_API_URL` 和相关配置即可。

---

*文档创建时间: 2025-01-24*
*版本: v1.0*
*状态: 本地模型支持已完成*
