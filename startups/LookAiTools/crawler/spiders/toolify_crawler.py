#!/usr/bin/env python3
"""
Toolify.ai 专用爬虫
"""
import os
import sys
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.common import *

class ToolifyCrawler:
    def __init__(self):
        self.base_url = "https://www.toolify.ai"
        self.target_url = "https://www.toolify.ai/new"
        self.platform = "toolify"
        self.tools_data = {}
        self.existing_tools = set()  # 存储已爬取的工具名称
        self._load_existing_data()

    def _load_existing_data(self):
        """加载已有的数据文件，获取已爬取的工具名称"""
        import os
        import json
        import glob

        data_dir = f"datas/{self.platform}"
        if not os.path.exists(data_dir):
            print("📁 数据目录不存在，开始全新爬取")
            return

        # 查找所有JSON文件
        json_files = glob.glob(f"{data_dir}/*.json")

        if not json_files:
            print("📁 未找到已有数据文件，开始全新爬取")
            return

        print(f"📁 发现 {len(json_files)} 个数据文件，正在加载已爬取的工具...")

        total_existing = 0
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if 'tools' in data:
                    for tool_data in data['tools'].values():
                        if 'name' in tool_data:
                            tool_name = tool_data['name'].strip()
                            if tool_name:
                                self.existing_tools.add(tool_name)
                                total_existing += 1

                                # 同时加载到当前数据中（如果有website_url）
                                if 'website_url' in tool_data:
                                    self.tools_data[tool_data['website_url']] = tool_data

            except Exception as e:
                print(f"   ⚠️ 加载文件失败 {json_file}: {e}")
                continue

        print(f"✅ 已加载 {total_existing} 个已爬取的工具")
        print(f"📊 当前数据库中有 {len(self.tools_data)} 个完整工具数据")

        if total_existing > 0:
            print("🔄 将跳过已爬取的工具，只处理新工具")

    async def crawl_all_tools(self):
        """爬取所有工具数据"""
        print(f"🚀 开始爬取Toolify.ai所有工具")
        print("=" * 60)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                })

                print(f"📱 访问列表页: {self.target_url}")
                try:
                    await page.goto(self.target_url, wait_until='domcontentloaded', timeout=60000)
                    await page.wait_for_timeout(5000)
                    print(f"✅ 页面加载完成")
                except Exception as e:
                    print(f"❌ 页面加载失败: {e}")
                    return {}

                # 边滚动边处理工具数据
                await self._scroll_and_process_tools(page)
                
                return self.tools_data
                
            except Exception as e:
                print(f"❌ 爬取失败: {e}")
                import traceback
                traceback.print_exc()
                return {}
            
            finally:
                await browser.close()

    async def _scroll_and_process_tools(self, page):
        """边滚动边处理工具数据"""
        print("🔄 开始边滚动边处理工具...")

        processed_hrefs = set()  # 记录已处理的工具
        total_processed = 0
        scroll_count = 0
        max_scrolls = 50
        no_new_tools_count = 0
        max_no_new = 5

        # 首先处理初始页面的工具
        print("📊 处理初始页面工具...")
        content = await page.content()
        soup = BeautifulSoup(content, 'html.parser')
        initial_tools = soup.select('.grid a[href*="/tool/"]')
        print(f"   📊 初始页面发现 {len(initial_tools)} 个工具")

        # 处理初始工具
        for tool in initial_tools:
            href = tool.get('href', '')
            if href:
                processed_hrefs.add(href)

        # 处理初始工具数据
        processed_count = await self._process_tools_batch(page, initial_tools, total_processed)
        total_processed += processed_count

        while scroll_count < max_scrolls:
            scroll_count += 1
            print(f"\n🔄 第{scroll_count}次滚动处理...")

            # 获取当前页面的工具
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            current_tools = soup.select('.grid a[href*="/tool/"]')

            # 找出新的工具
            new_tools = []
            for tool in current_tools:
                href = tool.get('href', '')
                if href and href not in processed_hrefs:
                    new_tools.append(tool)
                    processed_hrefs.add(href)

            print(f"   📊 发现 {len(new_tools)} 个新工具，总计已发现 {len(processed_hrefs)} 个")

            if not new_tools:
                no_new_tools_count += 1
                print(f"   ⏳ 连续 {no_new_tools_count} 次无新工具")

                if no_new_tools_count >= max_no_new:
                    print(f"   ✅ 无更多新工具，停止滚动")
                    break
            else:
                no_new_tools_count = 0

                # 批量处理新发现的工具
                processed_count = await self._process_tools_batch(page, new_tools, total_processed)
                total_processed += processed_count

                print(f"\n📊 本轮处理完成: 成功获取 {len(self.tools_data)} 个工具")

            # 滚动到页面底部
            try:
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(3000)

                # 等待网络空闲
                try:
                    await page.wait_for_load_state('networkidle', timeout=10000)
                except:
                    pass

            except Exception as e:
                print(f"   ⚠️ 滚动时出错: {e}")
                break

        print(f"\n🎯 滚动处理完成！")
        print(f"📊 总计发现 {len(processed_hrefs)} 个工具链接")
        print(f"✅ 成功获取 {len(self.tools_data)} 个完整工具数据")

    async def _process_tools_batch(self, page, tools, start_index=0):
        """批量处理工具"""
        for i, tool in enumerate(tools, start_index + 1):
            print(f"\n📋 处理工具 {i}")

            # 获取基本信息
            basic_info = self._extract_basic_info(tool)
            if not basic_info or not basic_info["toolify_url"]:
                print(f"   ❌ 跳过无效工具")
                continue

            print(f"   📝 {basic_info['name']}")

            # 检查是否已经爬取过
            if basic_info['name'] in self.existing_tools:
                print(f"   ⏭️ 工具已爬取过，跳过")
                continue

            # 检查是否在当前数据中已存在
            if any(t['name'] == basic_info['name'] for t in self.tools_data.values()):
                print(f"   ⏭️ 工具在当前数据中已存在，跳过")
                continue

            # 获取详细信息
            detailed_info = await self._get_detailed_info(page, basic_info["toolify_url"])

            if detailed_info and detailed_info.get("website_url"):
                # 清理网站URL
                clean_website_url = clean_url(detailed_info["website_url"])

                # 检查URL是否已存在
                if clean_website_url in self.tools_data:
                    print(f"   ⏭️ URL已存在，跳过")
                    continue

                # 获取官网信息
                landpage_info = await self._get_landpage_info(page, clean_website_url, basic_info["name"])

                # 合并信息
                complete_info = {
                    "name": basic_info["name"],
                    "website_url": clean_website_url,
                    "product_info": {
                        "description": detailed_info.get("description", ""),
                        "features": detailed_info.get("features", []),
                        "pricing": detailed_info.get("pricing", ""),
                        "category": detailed_info.get("category", ""),
                        "tags": detailed_info.get("tags", [])
                    },
                    "landpage": landpage_info
                }

                # 保存数据
                if clean_website_url:
                    self.tools_data[clean_website_url] = complete_info
                    # 添加到已爬取工具集合
                    self.existing_tools.add(basic_info['name'])
                    print(f"   ✅ {clean_website_url}")

                # 避免请求过快
                await asyncio.sleep(1)
            else:
                print(f"   ❌ 未获取到详细信息")

        return len(tools)

    async def _get_all_tools_with_scroll(self, page):
        """通过滚动加载获取所有工具"""
        print("🔄 开始滚动加载所有工具...")

        # 首先获取初始工具
        content = await page.content()
        soup = BeautifulSoup(content, 'html.parser')
        initial_tools = soup.select('.grid a[href*="/tool/"]')
        print(f"   📊 初始发现 {len(initial_tools)} 个工具")

        all_tool_hrefs = set()  # 使用href作为唯一标识
        all_tools = []  # 存储实际的元素

        # 添加初始工具
        for tool in initial_tools:
            href = tool.get('href', '')
            if href:
                all_tool_hrefs.add(href)
                all_tools.append(tool)

        last_count = len(all_tools)
        no_change_count = 0
        max_no_change = 5  # 连续5次没有新增就停止
        scroll_count = 0
        max_scrolls = 50  # 最大滚动次数限制

        while scroll_count < max_scrolls:
            scroll_count += 1

            # 获取当前页面的工具链接
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')

            # 查找工具链接 - 尝试多种选择器
            selectors = [
                '.grid a[href*="/tool/"]',
                'a[href*="/tool/"]',
                '[href*="/tool/"]'
            ]

            current_tools = []
            for selector in selectors:
                tools = soup.select(selector)
                if tools:
                    current_tools = tools
                    break

            # 添加新发现的工具
            new_tools_count = 0
            for tool in current_tools:
                href = tool.get('href', '')
                if href and href not in all_tool_hrefs:
                    all_tool_hrefs.add(href)
                    all_tools.append(tool)
                    new_tools_count += 1

            current_count = len(all_tools)
            print(f"   📊 第{scroll_count}次滚动: 发现 {new_tools_count} 个新工具，总计 {current_count} 个")

            # 检查是否有新增
            if current_count == last_count:
                no_change_count += 1
                print(f"   ⏳ 连续 {no_change_count} 次无新增工具")

                if no_change_count >= max_no_change:
                    print(f"   ✅ 已获取所有工具，停止滚动")
                    break
            else:
                no_change_count = 0
                last_count = current_count

            # 滚动到页面底部
            try:
                await page.evaluate("""
                    window.scrollTo(0, document.body.scrollHeight);
                """)

                # 等待新内容加载
                await page.wait_for_timeout(4000)

                # 尝试等待网络空闲
                try:
                    await page.wait_for_load_state('networkidle', timeout=10000)
                except:
                    pass

            except Exception as e:
                print(f"   ⚠️ 滚动时出错: {e}")
                break

            # 安全检查：如果工具数量超过合理范围，停止
            if current_count > 5000:  # 设置一个合理的上限
                print(f"   ⚠️ 工具数量超过上限 ({current_count})，停止滚动")
                break

        print(f"🎯 滚动完成，共发现 {len(all_tools)} 个工具")
        return all_tools

    def _extract_basic_info(self, link_elem):
        """提取基本信息"""
        try:
            # 获取Toolify页面URL
            href = link_elem.get('href', '')
            if not href:
                return None
            
            toolify_url = f"{self.base_url}{href}" if href.startswith('/') else href
            
            # 获取工具名称
            name_elem = link_elem.select_one('h1, h2, h3, h4, h5, h6')
            name = name_elem.get_text().strip() if name_elem else ""
            
            # 从URL提取名称作为备选
            if not name and '/tool/' in href:
                tool_slug = href.split('/tool/')[-1].split('?')[0]
                name = tool_slug.replace('-', ' ').title()
            
            # 获取图片
            img_elem = link_elem.select_one('img')
            image = ""
            if img_elem:
                img_src = img_elem.get('src') or img_elem.get('data-src')
                if img_src:
                    image = f"{self.base_url}{img_src}" if img_src.startswith('/') else img_src
            
            return {
                "name": name,
                "toolify_url": toolify_url,
                "image": image
            } if name else None
            
        except Exception as e:
            print(f"   ❌ 提取基本信息失败: {e}")
            return None
    
    async def _get_detailed_info(self, page, toolify_url):
        """获取详细信息"""
        try:
            print(f"   🔍 访问详情页...")
            await page.goto(toolify_url, wait_until='networkidle', timeout=15000)
            await page.wait_for_timeout(2000)
            
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            detailed_info = {}
            
            # 查找真实网站URL
            website_url = await self._find_website_url(page, soup)
            if website_url:
                detailed_info["website_url"] = website_url
            
            # 从面包屑获取分类
            category = self._extract_category_from_breadcrumb(soup)
            if category:
                detailed_info["category"] = category
            
            # 从标签区域获取标签
            tags = self._extract_tags_from_page(soup)
            if tags:
                detailed_info["tags"] = tags
            
            # 提取详细描述
            description = self._extract_detailed_description(soup)
            if description:
                detailed_info["description"] = description
            
            # 提取功能特性
            features = self._extract_features(soup)
            if features:
                detailed_info["features"] = features
            
            # 提取价格信息
            pricing = self._extract_pricing(soup)
            if pricing:
                detailed_info["pricing"] = pricing
            
            return detailed_info
            
        except Exception as e:
            print(f"     ❌ 获取详细信息失败: {e}")
            return {}
    
    async def _find_website_url(self, page, soup):
        """查找网站URL"""
        try:
            # 查找"Open site"按钮
            visit_selectors = [
                'a:has-text("Open site")',
                'a:has-text("Visit Website")',
                'a:has-text("Visit")'
            ]
            
            for selector in visit_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        href = await element.get_attribute('href')
                        if href and is_valid_external_url(href):
                            return href
                except:
                    continue
            
            return None
            
        except Exception as e:
            return None
    
    def _extract_category_from_breadcrumb(self, soup):
        """从面包屑导航提取分类"""
        try:
            breadcrumb = soup.select_one('[class*="breadcrumb"]')
            if breadcrumb:
                text = breadcrumb.get_text().strip()
                parts = [part.strip() for part in text.split('\n') if part.strip()]
                if len(parts) >= 3:
                    category = parts[1]
                    if category and category.lower() != 'home':
                        return category
            
            return ""
            
        except Exception as e:
            return ""
    
    def _extract_tags_from_page(self, soup):
        """从页面提取标签"""
        try:
            tags = []
            
            # 查找标签容器
            tag_container = soup.select_one('.flex.items-start.flex-wrap.mt-5.gap-x-2\\.5.gap-y-2\\.5')
            if tag_container:
                tag_elements = tag_container.select('span, a, div')
                for elem in tag_elements:
                    tag_text = elem.get_text().strip()
                    if (tag_text and len(tag_text) < 50 and 
                        tag_text not in tags and
                        tag_text not in ['Website', 'Freemium', 'Paid']):
                        tags.append(tag_text)
            
            return tags[:8]
            
        except Exception as e:
            return []
    
    def _extract_detailed_description(self, soup):
        """提取详细描述"""
        try:
            desc_selectors = [
                '.description', '.tool-description', '.content',
                '[class*="description"]', '.summary', '.overview'
            ]
            
            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    desc = desc_elem.get_text().strip()
                    if desc and len(desc) > 50:
                        return desc[:500]
            
            return ""
            
        except Exception as e:
            return ""
    
    def _extract_features(self, soup):
        """提取功能特性"""
        try:
            feature_selectors = [
                '.features li', '.feature-list li', '.benefits li',
                '.highlights li', 'ul li'
            ]
            
            for selector in feature_selectors:
                feature_elems = soup.select(selector)
                if feature_elems and len(feature_elems) > 2:
                    features = []
                    for elem in feature_elems[:8]:
                        feature = elem.get_text().strip()
                        if (feature and len(feature) > 10 and len(feature) < 200 and 
                            not any(skip in feature.lower() for skip in ['toolify', 'visit', 'website'])):
                            features.append(feature)
                    
                    if len(features) >= 2:
                        return features
            
            return []
            
        except Exception as e:
            return []
    
    def _extract_pricing(self, soup):
        """提取价格信息"""
        try:
            pricing_selectors = [
                '.pricing', '.price', '.cost',
                '[class*="pricing"]', '[class*="price"]'
            ]
            
            for selector in pricing_selectors:
                pricing_elem = soup.select_one(selector)
                if pricing_elem:
                    pricing = pricing_elem.get_text().strip()
                    if pricing and ('$' in pricing or 'free' in pricing.lower() or 'paid' in pricing.lower()):
                        return pricing[:100]
            
            return ""
            
        except Exception as e:
            return ""

    async def _get_landpage_info(self, page, website_url, tool_name):
        """获取官网信息"""
        try:
            if not website_url:
                return {}

            print(f"     🌐 访问官网: {website_url}")

            # 设置标准的浏览器窗口大小
            await page.set_viewport_size({"width": 1920, "height": 1080})

            # 访问官网
            await page.goto(website_url, wait_until='networkidle', timeout=20000)
            await page.wait_for_timeout(3000)

            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')

            landpage_info = {
                "favicon": "",
                "logo": "",
                "hero_image": "",
                "page_screenshot": "",
                "full_page_text": "",
                "page_title": "",
                "headings": [],
                "links": [],
                "meta_data": {}
            }

            # 获取网站图标
            favicon = self._extract_favicon(soup, website_url)
            if favicon:
                landpage_info["favicon"] = favicon

            # 获取Logo
            logo = self._extract_logo(soup, website_url)
            if logo:
                landpage_info["logo"] = logo

            # 获取主要图片
            hero_image = self._extract_hero_image(soup, website_url)
            if hero_image:
                landpage_info["hero_image"] = hero_image

            # 保存页面截图
            screenshot_path = await save_screenshot(page, tool_name, self.platform)
            if screenshot_path:
                landpage_info["page_screenshot"] = screenshot_path

            # 获取完整页面文本
            full_text = extract_page_text(soup)
            if full_text:
                landpage_info["full_page_text"] = full_text

            # 获取页面标题
            page_title = soup.select_one('title')
            if page_title:
                landpage_info["page_title"] = page_title.get_text().strip()

            # 获取所有标题
            headings = extract_headings(soup)
            if headings:
                landpage_info["headings"] = headings

            # 获取所有链接
            links = extract_links(soup, website_url)
            if links:
                landpage_info["links"] = links

            # 获取meta数据
            meta_data = extract_page_meta(soup)
            if meta_data:
                landpage_info["meta_data"] = meta_data

            return landpage_info

        except Exception as e:
            print(f"     ❌ 获取官网信息失败: {e}")
            return {}

    def _extract_favicon(self, soup, base_url):
        """提取网站图标"""
        try:
            favicon_selectors = [
                'link[rel="icon"]',
                'link[rel="shortcut icon"]',
                'link[rel="apple-touch-icon"]',
                'link[rel="favicon"]'
            ]

            for selector in favicon_selectors:
                favicon_elem = soup.select_one(selector)
                if favicon_elem:
                    href = favicon_elem.get('href', '')
                    if href:
                        return resolve_url(href, base_url)

            # 默认favicon路径
            return resolve_url('/favicon.ico', base_url)

        except Exception as e:
            return ""

    def _extract_logo(self, soup, base_url):
        """提取Logo"""
        try:
            logo_selectors = [
                'img[alt*="logo" i]',
                'img[class*="logo" i]',
                'img[id*="logo" i]',
                '.logo img',
                '.header img',
                '.navbar img',
                'header img'
            ]

            for selector in logo_selectors:
                logo_elem = soup.select_one(selector)
                if logo_elem:
                    src = logo_elem.get('src') or logo_elem.get('data-src')
                    if src:
                        return resolve_url(src, base_url)

            return ""

        except Exception as e:
            return ""

    def _extract_hero_image(self, soup, base_url):
        """提取主要图片"""
        try:
            hero_selectors = [
                '.hero img',
                '.banner img',
                '.main-image img',
                '.featured-image img',
                '.product-image img',
                'main img',
                '.landing img'
            ]

            for selector in hero_selectors:
                hero_elem = soup.select_one(selector)
                if hero_elem:
                    src = hero_elem.get('src') or hero_elem.get('data-src')
                    if src and is_valid_image_url(src):
                        return resolve_url(src, base_url)

            return ""

        except Exception as e:
            return ""

    def save_results(self):
        """保存结果"""
        timestamp_info = get_timestamp()

        result = {
            "source": "toolify_crawler",
            "platform": self.platform,
            "target_url": self.target_url,
            "crawl_time": timestamp_info["timestamp"],
            "crawl_time_str": timestamp_info["datetime"],
            "total_tools": len(self.tools_data),
            "tools": self.tools_data
        }

        # 保存到指定目录
        filename = f"datas/{self.platform}/toolify_complete_{int(timestamp_info['timestamp'])}.json"

        if save_json(result, filename):
            print(f"\n✅ 结果已保存到: {filename}")
            return filename
        else:
            return None

    def print_summary(self):
        """打印总结"""
        if not self.tools_data:
            print("❌ 没有数据可显示")
            return

        print(f"\n📊 爬取总结")
        print("=" * 50)
        print(f"总工具数: {len(self.tools_data)}")

        # 统计分类
        categories = {}
        for tool in self.tools_data.values():
            category = tool['product_info'].get('category', '未分类')
            categories[category] = categories.get(category, 0) + 1

        if categories:
            print(f"分类分布:")
            for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                print(f"  {category}: {count} 个")

async def main():
    """主函数"""
    print("🚀 Toolify.ai 完整爬虫")
    print("=" * 60)

    crawler = ToolifyCrawler()

    # 爬取所有工具数据
    tools = await crawler.crawl_all_tools()

    if tools:
        # 保存结果
        filename = crawler.save_results()

        # 打印总结
        crawler.print_summary()

        print(f"\n🎉 爬取完成！")
        print(f"📁 结果文件: {filename}")
        print(f"📊 成功获取 {len(tools)} 个工具")

    else:
        print(f"\n❌ 爬取失败，未获取到数据")

if __name__ == "__main__":
    asyncio.run(main())
