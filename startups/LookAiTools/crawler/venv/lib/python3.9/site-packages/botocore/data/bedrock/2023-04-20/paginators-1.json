{"pagination": {"ListCustomModels": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelSummaries"}, "ListModelCustomizationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelCustomizationJobSummaries"}, "ListProvisionedModelThroughputs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "provisionedModelSummaries"}, "ListEvaluationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "jobSummaries"}, "ListGuardrails": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "guardrails"}, "ListModelCopyJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelCopyJobSummaries"}, "ListModelInvocationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "invocationJobSummaries"}, "ListImportedModels": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelSummaries"}, "ListModelImportJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelImportJobSummaries"}, "ListInferenceProfiles": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "inferenceProfileSummaries"}, "ListMarketplaceModelEndpoints": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "marketplaceModelEndpoints"}, "ListPromptRouters": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "promptRouterSummaries"}, "ListCustomModelDeployments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelDeploymentSummaries"}}}